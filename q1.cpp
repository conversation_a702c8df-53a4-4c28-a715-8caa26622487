#include <iostream>
#include <vector>
using namespace std;

int bagsSum(int target, int nbags, int n, int* V)
{
    vector<vector<int>> dp(nbags + 1, vector<int>(target + 1, 0));
    dp[0][0] = 1;
    for (int i = 0; i < n; i++) {
        int weight = V[i];
        for (int bags = min(nbags, i + 1); bags >= 1; bags--) {
            for (int sum = target; sum >= weight; sum--) {
                dp[bags][sum] += dp[bags - 1][sum - weight];
            }
        }
    }

    return dp[nbags][target];
}

int main()
{
    int target, nbags, n;
    cin >> target >> nbags >> n;
    int V[n];
    for (int i = 0; i < n; i++)
    {
        cin >> V[i];
    }
    cout << bagsSum(target, nbags, n, V);
    return 0;
}
