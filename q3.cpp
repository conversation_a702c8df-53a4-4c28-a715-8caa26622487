#include <bits/stdc++.h>
using namespace std;

typedef long long llint;

llint calculateCost(int n, llint arr[], llint waterLevel) {
    llint cost = 0;
    int mid = n / 2;
    for (int i = 0; i < n; i++) {
        llint targetHeight;
        if (i <= mid) {
            targetHeight = waterLevel + (mid - i);
        } else {
            targetHeight = waterLevel + (i - mid);
        }
        cost += abs(arr[i] - targetHeight);
    }
    return cost;
}

llint findMoves(int n, llint a[], llint b[]) {
    vector<llint> candidates;

    for (int i = 0; i < n; i++) {
        candidates.push_back(a[i]);
        candidates.push_back(b[i]);
    }

    int mid = n / 2;
    for (int i = 0; i < n; i++) {
        if (i <= mid) {
            candidates.push_back(a[i] - (mid - i));
            candidates.push_back(b[i] - (mid - i));
        } else {
            candidates.push_back(a[i] - (i - mid));
            candidates.push_back(b[i] - (i - mid));
        }
    }

    sort(candidates.begin(), candidates.end());
    candidates.erase(unique(candidates.begin(), candidates.end()), candidates.end());

    llint minMoves = LLONG_MAX;

    for (llint waterLevel : candidates) {
        if (waterLevel < 0) continue;
        llint costA = calculateCost(n, a, waterLevel);
        llint costB = calculateCost(n, b, waterLevel);
        llint totalCost = costA + costB;
        minMoves = min(minMoves, totalCost);
    }

    return minMoves;
}

int main() {
    int n;
    cin >> n;
    llint a[n], b[n];
    for (int i = 0; i < n; i++) cin >> a[i];
    for (int i = 0; i < n; i++) cin >> b[i];
    cout << findMoves(n, a, b);
    return 0;
}
